/**
 * 错误处理和日志记录模块
 * 提供统一的错误处理和日志记录功能
 */

export interface ErrorLog {
  id: string;
  timestamp: number;
  level: 'error' | 'warning' | 'info' | 'debug';
  message: string;
  details?: any;
  stack?: string;
  context?: string;
}

export class ErrorHandler {
  private logs: ErrorLog[] = [];
  private maxLogs = 1000; // 最多保存1000条日志

  constructor() {
    this.setupGlobalErrorHandlers();
  }

  /**
   * 设置全局错误处理器
   */
  private setupGlobalErrorHandlers(): void {
    // 捕获未处理的错误
    window.addEventListener('error', (event) => {
      this.logError('Global Error', {
        message: event.message,
        filename: event.filename,
        lineno: event.lineno,
        colno: event.colno,
        error: event.error
      });
    });

    // 捕获未处理的Promise拒绝
    window.addEventListener('unhandledrejection', (event) => {
      this.logError('Unhandled Promise Rejection', {
        reason: event.reason,
        promise: event.promise
      });
    });
  }

  /**
   * 记录错误
   */
  logError(message: string, details?: any, context?: string): void {
    const errorLog: ErrorLog = {
      id: this.generateId(),
      timestamp: Date.now(),
      level: 'error',
      message,
      details,
      stack: new Error().stack,
      context
    };

    this.addLog(errorLog);
    console.error(`[Enhanced O-Spy Error] ${message}`, details);
  }

  /**
   * 记录警告
   */
  logWarning(message: string, details?: any, context?: string): void {
    const warningLog: ErrorLog = {
      id: this.generateId(),
      timestamp: Date.now(),
      level: 'warning',
      message,
      details,
      context
    };

    this.addLog(warningLog);
    console.warn(`[Enhanced O-Spy Warning] ${message}`, details);
  }

  /**
   * 记录信息
   */
  logInfo(message: string, details?: any, context?: string): void {
    const infoLog: ErrorLog = {
      id: this.generateId(),
      timestamp: Date.now(),
      level: 'info',
      message,
      details,
      context
    };

    this.addLog(infoLog);
    console.log(`[Enhanced O-Spy Info] ${message}`, details);
  }

  /**
   * 记录调试信息
   */
  logDebug(message: string, details?: any, context?: string): void {
    const debugLog: ErrorLog = {
      id: this.generateId(),
      timestamp: Date.now(),
      level: 'debug',
      message,
      details,
      context
    };

    this.addLog(debugLog);
    console.debug(`[Enhanced O-Spy Debug] ${message}`, details);
  }

  /**
   * 添加日志到列表
   */
  private addLog(log: ErrorLog): void {
    this.logs.unshift(log);
    
    // 保持日志数量在限制内
    if (this.logs.length > this.maxLogs) {
      this.logs = this.logs.slice(0, this.maxLogs);
    }
  }

  /**
   * 获取所有日志
   */
  getLogs(level?: 'error' | 'warning' | 'info' | 'debug'): ErrorLog[] {
    if (level) {
      return this.logs.filter(log => log.level === level);
    }
    return [...this.logs];
  }

  /**
   * 清空日志
   */
  clearLogs(): void {
    this.logs = [];
    this.logInfo('日志已清空');
  }

  /**
   * 导出日志
   */
  exportLogs(): void {
    const exportData = {
      exportTime: new Date().toISOString(),
      totalLogs: this.logs.length,
      logs: this.logs.map(log => ({
        ...log,
        timestamp: new Date(log.timestamp).toISOString()
      }))
    };

    const blob = new Blob([JSON.stringify(exportData, null, 2)], {
      type: 'application/json'
    });

    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `enhanced-ospy-logs-${Date.now()}.json`;
    link.click();
    URL.revokeObjectURL(url);

    this.logInfo('日志导出完成');
  }

  /**
   * 获取错误统计
   */
  getErrorStats(): {
    total: number;
    errors: number;
    warnings: number;
    infos: number;
    debugs: number;
    recentErrors: ErrorLog[];
  } {
    const stats = {
      total: this.logs.length,
      errors: this.logs.filter(log => log.level === 'error').length,
      warnings: this.logs.filter(log => log.level === 'warning').length,
      infos: this.logs.filter(log => log.level === 'info').length,
      debugs: this.logs.filter(log => log.level === 'debug').length,
      recentErrors: this.logs.filter(log => log.level === 'error').slice(0, 5)
    };

    return stats;
  }

  /**
   * 安全执行函数，捕获错误
   */
  async safeExecute<T>(
    fn: () => Promise<T> | T,
    context: string,
    fallback?: T
  ): Promise<T | undefined> {
    try {
      const result = await fn();
      return result;
    } catch (error) {
      this.logError(`执行失败: ${context}`, {
        error: error.message,
        stack: error.stack
      }, context);
      
      return fallback;
    }
  }

  /**
   * 包装异步函数，自动错误处理
   */
  wrapAsync<T extends any[], R>(
    fn: (...args: T) => Promise<R>,
    context: string
  ): (...args: T) => Promise<R | undefined> {
    return async (...args: T) => {
      return this.safeExecute(() => fn(...args), context);
    };
  }

  /**
   * 生成唯一ID
   */
  private generateId(): string {
    return `log_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * 格式化错误信息用于显示
   */
  formatError(error: any): string {
    if (error instanceof Error) {
      return `${error.name}: ${error.message}`;
    }
    
    if (typeof error === 'string') {
      return error;
    }
    
    try {
      return JSON.stringify(error);
    } catch {
      return String(error);
    }
  }

  /**
   * 检查是否有严重错误
   */
  hasCriticalErrors(): boolean {
    const recentErrors = this.logs
      .filter(log => log.level === 'error')
      .filter(log => Date.now() - log.timestamp < 5 * 60 * 1000); // 5分钟内的错误
    
    return recentErrors.length > 3; // 5分钟内超过3个错误认为是严重问题
  }

  /**
   * 获取健康状态
   */
  getHealthStatus(): {
    status: 'healthy' | 'warning' | 'critical';
    message: string;
    details: any;
  } {
    const stats = this.getErrorStats();
    
    if (this.hasCriticalErrors()) {
      return {
        status: 'critical',
        message: '检测到严重错误，建议重新加载页面',
        details: stats
      };
    }
    
    if (stats.errors > 0) {
      return {
        status: 'warning',
        message: '存在一些错误，但系统仍可正常运行',
        details: stats
      };
    }
    
    return {
      status: 'healthy',
      message: '系统运行正常',
      details: stats
    };
  }
}
